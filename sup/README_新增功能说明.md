# 供货卡密管理系统 - 新增功能说明

## 概述

本次更新为供货卡密管理系统添加了两个重要功能：
1. **一键删除所有卡密功能**
2. **一键导出所有卡密功能**

## 功能详情

### 1. 一键删除所有卡密功能

#### 功能描述
- 支持按商品ID批量删除指定商品的所有卡密
- 包含完整的权限验证和安全检查
- 删除后自动更新商品库存信息

#### API接口
- **接口地址**: `sup/goods/cards.php?act=deleteAllCards`
- **请求方式**: POST
- **参数**:
  - `tid` (必需): 商品ID

#### 安全特性
- 权限验证：只能删除属于当前供货商的商品卡密
- 数据验证：检查商品是否存在
- 事务处理：使用数据库事务确保数据一致性
- 库存更新：删除后自动更新商品库存数量
- 详细统计：显示删除的卡密数量（包括已售出卡密）
- 错误处理：完整的错误信息反馈和回滚机制

#### 响应格式
```json
// 成功响应
{
    "code": 0,
    "message": "成功删除商品【商品名称】的所有 X 条卡密",
    "msg": "成功删除商品【商品名称】的所有 X 条卡密",
    "data": [],
    "timestamp": 1234567890
}

// 错误响应
{
    "code": -1,
    "message": "错误信息",
    "msg": "错误信息"
}
```

### 2. 一键导出所有卡密功能

#### 功能描述
- 支持按商品ID导出指定商品的所有卡密
- 导出格式为纯文本文件(.txt)
- 文件名包含商品名称和导出时间
- 支持卡号+密码格式和纯卡号格式

#### API接口
- **接口地址**: `sup/goods/cards.php?act=exportAllCards`
- **请求方式**: POST/GET
- **参数**:
  - `tid` (必需): 商品ID
  - `type` (可选): 导出类型
    - `all` (默认): 导出所有卡密
    - `unused`: 仅导出未使用的卡密
    - `used`: 仅导出已使用的卡密

#### 导出格式
- **文件内容格式**:
  - 文件头包含商品信息、导出时间等元数据
  - 有密码的卡密: `卡号 密码`
  - 无密码的卡密: `卡号`
  - 文件尾包含统计信息
- **文件名格式**: `卡密导出_商品名称_类型_YYYY-MM-DD_HH-mm-ss.txt`
  - 例如: `卡密导出_QQ会员_全部_2025-08-06_14-30-25.txt`

#### 安全特性
- 权限验证：只能导出属于当前供货商的商品卡密
- 数据验证：检查商品是否存在和是否有卡密可导出
- 文件名安全：自动过滤特殊字符，确保文件名安全

## 使用方法

### 通过API调用

#### JavaScript示例 - 删除所有卡密
```javascript
function deleteAllCards(tid) {
    if (!confirm('确定要删除所有卡密吗？此操作无法恢复！')) {
        return;
    }
    
    fetch('goods/cards.php?act=deleteAllCards', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `tid=${tid}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            alert('删除成功: ' + data.msg);
            location.reload(); // 刷新页面
        } else {
            alert('删除失败: ' + data.msg);
        }
    })
    .catch(error => {
        alert('网络错误: ' + error.message);
    });
}
```

#### JavaScript示例 - 导出所有卡密
```javascript
function exportAllCards(tid) {
    if (!confirm('确定要导出所有卡密吗？')) {
        return;
    }
    
    // 创建隐藏链接触发下载
    const link = document.createElement('a');
    link.href = `goods/cards.php?act=exportAllCards&tid=${tid}`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    alert('导出请求已发送，请检查下载');
}
```

### 测试页面

系统提供了两个测试页面来验证新功能：

1. **完整测试页面**: `sup/test_cards_api.html`
   - 无需登录的静态HTML页面
   - 包含完整的用户界面
   - 适合功能演示和测试

2. **简单测试页面**: `sup/test_api_simple.php`
   - 需要登录的PHP页面
   - 显示当前用户的商品列表
   - 提供实时测试功能

## 错误处理

### 常见错误及解决方案

1. **"商品ID不能为空"**
   - 检查请求参数中是否包含有效的tid参数

2. **"该商品不存在"**
   - 检查商品ID是否正确
   - 确认商品是否属于当前供货商

3. **"无权限操作该商品"**
   - 确认当前登录用户有权限操作该商品
   - 检查商品是否属于当前供货商

4. **"该商品没有卡密可删除/导出"**
   - 确认商品确实有卡密数据
   - 检查数据库中的卡密记录

## 数据库影响

### 删除功能
- 删除 `cmy_faka` 表中对应商品的所有记录
- 更新 `cmy_tools` 表中的库存信息

### 导出功能
- 只读取数据，不修改任何数据库记录

## 安全注意事项

1. **权限控制**: 所有操作都会验证当前用户权限
2. **数据验证**: 严格验证输入参数
3. **操作确认**: 删除操作需要用户确认
4. **日志记录**: 建议在生产环境中添加操作日志
5. **备份建议**: 删除操作前建议备份重要数据

## 技术实现

### 后端实现
- 基于现有的ThinkPHP框架
- 使用Db类进行数据库操作
- 遵循现有的代码规范和错误处理机制

### 前端集成
- 兼容现有的Vue.js前端框架
- 提供标准的REST API接口
- 支持AJAX调用和文件下载

## 版本信息

- **添加时间**: 2025-08-06
- **兼容版本**: 与现有系统完全兼容
- **依赖**: 无额外依赖，使用现有框架功能

## 联系支持

如有问题或需要技术支持，请联系开发团队。
