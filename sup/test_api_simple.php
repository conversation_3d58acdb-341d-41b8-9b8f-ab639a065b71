<?php
/**
 * 简单的API测试脚本
 * 用于测试新增的一键删除和一键导出卡密功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 包含必要的文件
include dirname(__DIR__) . "/includes/common.php";

// 检查是否已登录
if (!$isLogin3) {
    echo "<h1>测试API功能</h1>";
    echo "<p style='color: red;'>错误：未登录，无法测试API功能</p>";
    echo "<p>请先登录供货商后台，然后访问此页面进行测试</p>";
    echo "<a href='index.php'>返回登录页面</a>";
    exit;
}

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>卡密管理API测试</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }";
echo ".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo ".success { color: green; }";
echo ".error { color: red; }";
echo ".info { color: blue; }";
echo "button { padding: 8px 16px; margin: 5px; cursor: pointer; }";
echo ".btn-danger { background-color: #dc3545; color: white; border: none; }";
echo ".btn-success { background-color: #28a745; color: white; border: none; }";
echo ".btn-primary { background-color: #007bff; color: white; border: none; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>卡密管理API测试</h1>";
echo "<p class='info'>当前登录用户：" . $masterrow['user'] . " (ID: " . $masterrow['zid'] . ")</p>";

// 获取商品列表用于测试
use core\Db;

$goods = Db::name('tools')->where(['zid' => $masterrow['zid'], 'is_curl' => 4])->limit(10)->select();

if (empty($goods)) {
    echo "<p class='error'>没有找到可用的商品进行测试</p>";
    exit;
}

echo "<div class='section'>";
echo "<h3>可用商品列表</h3>";
echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
echo "<tr><th>商品ID</th><th>商品名称</th><th>库存</th><th>操作</th></tr>";

foreach ($goods as $item) {
    $cardCount = Db::name('faka')->where([
        'tid' => $item['tid'],
        'zid' => $masterrow['zid']
    ])->count('kid');
    
    echo "<tr>";
    echo "<td>" . $item['tid'] . "</td>";
    echo "<td>" . htmlspecialchars($item['name']) . "</td>";
    echo "<td>" . $cardCount . " 条卡密</td>";
    echo "<td>";
    echo "<button class='btn-primary' onclick='testGetCards(" . $item['tid'] . ")'>查看卡密</button><br>";
    echo "<button class='btn-success' onclick='testExport(" . $item['tid'] . ", \"" . addslashes($item['name']) . "\", \"all\")'>导出全部</button> ";
    echo "<button class='btn-success' onclick='testExport(" . $item['tid'] . ", \"" . addslashes($item['name']) . "\", \"unused\")'>导出未使用</button> ";
    echo "<button class='btn-success' onclick='testExport(" . $item['tid'] . ", \"" . addslashes($item['name']) . "\", \"used\")'>导出已使用</button><br>";
    if ($cardCount > 0) {
        echo "<button class='btn-danger' onclick='testDelete(" . $item['tid'] . ", \"" . addslashes($item['name']) . "\")'>删除所有</button>";
    }
    echo "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<div class='section'>";
echo "<h3>测试结果</h3>";
echo "<div id='testResult'></div>";
echo "</div>";

echo "<script>";
echo "function showResult(message, type = 'info') {";
echo "    const resultDiv = document.getElementById('testResult');";
echo "    const timestamp = new Date().toLocaleTimeString();";
echo "    resultDiv.innerHTML += '<p class=\"' + type + '\">[' + timestamp + '] ' + message + '</p>';";
echo "}";

echo "function testGetCards(tid) {";
echo "    showResult('正在获取商品 ' + tid + ' 的卡密列表...', 'info');";
echo "    fetch('goods/cards.php?act=getCardsData', {";
echo "        method: 'POST',";
echo "        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },";
echo "        body: 'tid=' + tid + '&pagesize=5'";
echo "    })";
echo "    .then(response => response.json())";
echo "    .then(data => {";
echo "        if (data.code === 0) {";
echo "            showResult('成功获取 ' + data.data.total + ' 条卡密', 'success');";
echo "            if (data.data.rows.length > 0) {";
echo "                let cards = data.data.rows.map(card => card.km + (card.pw ? ' ' + card.pw : '')).join(', ');";
echo "                showResult('前几条卡密: ' + cards, 'info');";
echo "            }";
echo "        } else {";
echo "            showResult('获取卡密失败: ' + data.msg, 'error');";
echo "        }";
echo "    })";
echo "    .catch(error => showResult('网络错误: ' + error.message, 'error'));";
echo "}";

echo "function testExport(tid, name, type) {";
echo "    type = type || 'all';";
echo "    const typeText = type === 'unused' ? '未使用的' : (type === 'used' ? '已使用的' : '所有');";
echo "    if (!confirm('确定要导出商品 \"' + name + '\" 的' + typeText + '卡密吗？')) return;";
echo "    showResult('正在导出商品 ' + tid + ' 的' + typeText + '卡密...', 'info');";
echo "    const link = document.createElement('a');";
echo "    link.href = 'goods/cards.php?act=exportAllCards&tid=' + tid + '&type=' + type;";
echo "    link.style.display = 'none';";
echo "    document.body.appendChild(link);";
echo "    link.click();";
echo "    document.body.removeChild(link);";
echo "    showResult('导出请求已发送，请检查下载', 'success');";
echo "}";

echo "function testDelete(tid, name) {";
echo "    if (!confirm('确定要删除商品 \"' + name + '\" 的所有卡密吗？\\n\\n此操作无法恢复！')) return;";
echo "    showResult('正在删除商品 ' + tid + ' 的所有卡密...', 'info');";
echo "    fetch('goods/cards.php?act=deleteAllCards', {";
echo "        method: 'POST',";
echo "        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },";
echo "        body: 'tid=' + tid";
echo "    })";
echo "    .then(response => response.json())";
echo "    .then(data => {";
echo "        if (data.code === 0) {";
echo "            showResult('删除成功: ' + data.msg, 'success');";
echo "            setTimeout(() => location.reload(), 2000);";
echo "        } else {";
echo "            showResult('删除失败: ' + data.msg, 'error');";
echo "        }";
echo "    })";
echo "    .catch(error => showResult('网络错误: ' + error.message, 'error'));";
echo "}";

echo "showResult('测试页面加载完成，可以开始测试API功能', 'success');";
echo "</script>";

echo "</body>";
echo "</html>";
?>
