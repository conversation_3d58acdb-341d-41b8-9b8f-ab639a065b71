<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>卡密管理API测试页面</h1>
        <p>此页面用于测试新增的一键删除和一键导出卡密功能</p>

        <div class="section">
            <h3>获取商品列表</h3>
            <button onclick="getGoods()">获取商品列表</button>
            <div id="goodsResult" class="result"></div>
            <div class="form-group">
                <label for="goodsSelect">选择商品:</label>
                <select id="goodsSelect">
                    <option value="">请先获取商品列表</option>
                </select>
            </div>
        </div>

        <div class="section">
            <h3>一键删除所有卡密</h3>
            <p style="color: red;">⚠️ 警告：此操作将删除选定商品的所有卡密，无法恢复！</p>
            <button class="btn-danger" onclick="deleteAllCards()">删除所有卡密</button>
            <div id="deleteResult" class="result"></div>
        </div>

        <div class="section">
            <h3>一键导出所有卡密</h3>
            <p>导出选定商品的所有卡密为txt文件</p>
            <button class="btn-success" onclick="exportAllCards()">导出所有卡密</button>
            <div id="exportResult" class="result"></div>
        </div>

        <div class="section">
            <h3>查看卡密列表</h3>
            <button onclick="getCards()">获取卡密列表</button>
            <div id="cardsResult" class="result"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
            element.style.display = 'block';
        }

        function getSelectedTid() {
            const select = document.getElementById('goodsSelect');
            const tid = select.value;
            if (!tid) {
                alert('请先选择一个商品');
                return null;
            }
            return tid;
        }

        function getGoods() {
            fetch('goods/cards.php?act=getGoods', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    const select = document.getElementById('goodsSelect');
                    select.innerHTML = '<option value="">请选择商品</option>';
                    data.data.rows.forEach(item => {
                        const option = document.createElement('option');
                        option.value = item.tid;
                        option.textContent = `${item.name} (ID: ${item.tid})`;
                        select.appendChild(option);
                    });
                    showResult('goodsResult', `成功获取 ${data.data.rows.length} 个商品`);
                } else {
                    showResult('goodsResult', data.msg || '获取商品列表失败', false);
                }
            })
            .catch(error => {
                showResult('goodsResult', '网络错误: ' + error.message, false);
            });
        }

        function deleteAllCards() {
            const tid = getSelectedTid();
            if (!tid) return;

            const goodsName = document.getElementById('goodsSelect').selectedOptions[0].textContent;
            
            if (!confirm(`确定要删除商品 "${goodsName}" 的所有卡密吗？\n\n此操作无法恢复！`)) {
                return;
            }

            fetch('goods/cards.php?act=deleteAllCards', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `tid=${tid}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    showResult('deleteResult', data.msg || '删除成功');
                } else {
                    showResult('deleteResult', data.msg || '删除失败', false);
                }
            })
            .catch(error => {
                showResult('deleteResult', '网络错误: ' + error.message, false);
            });
        }

        function exportAllCards() {
            const tid = getSelectedTid();
            if (!tid) return;

            const goodsName = document.getElementById('goodsSelect').selectedOptions[0].textContent;
            
            if (!confirm(`确定要导出商品 "${goodsName}" 的所有卡密吗？`)) {
                return;
            }

            // 创建一个隐藏的链接来触发下载
            const link = document.createElement('a');
            link.href = `goods/cards.php?act=exportAllCards&tid=${tid}`;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showResult('exportResult', '导出请求已发送，请检查下载');
        }

        function getCards() {
            const tid = getSelectedTid();
            if (!tid) return;

            fetch('goods/cards.php?act=getCardsData', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `tid=${tid}&pagesize=10`
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    const cards = data.data.rows;
                    let message = `共找到 ${data.data.total} 条卡密`;
                    if (cards.length > 0) {
                        message += `\n前 ${cards.length} 条:\n`;
                        cards.forEach((card, index) => {
                            message += `${index + 1}. ${card.km}${card.pw ? ' ' + card.pw : ''}\n`;
                        });
                    }
                    showResult('cardsResult', message);
                } else {
                    showResult('cardsResult', data.msg || '获取卡密列表失败', false);
                }
            })
            .catch(error => {
                showResult('cardsResult', '网络错误: ' + error.message, false);
            });
        }

        // 页面加载时自动获取商品列表
        window.onload = function() {
            getGoods();
        };
    </script>
</body>
</html>
